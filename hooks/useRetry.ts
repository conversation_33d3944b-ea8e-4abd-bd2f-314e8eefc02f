/**
 * Hook for handling retry operations with state management
 */

import { useState, useCallback, useRef } from 'react';
import { retryUtils, RetryOptions, RetryResult } from '@/utils/retryUtils';

export interface UseRetryState {
  isRetrying: boolean;
  attempts: number;
  lastError: any;
  totalTime: number;
}

export interface UseRetryReturn<T> {
  state: UseRetryState;
  retry: (operation: () => Promise<T>, options?: RetryOptions) => Promise<RetryResult<T>>;
  retryFetch: (url: string, init?: RequestInit, options?: RetryOptions) => Promise<RetryResult<Response>>;
  cancel: () => void;
  reset: () => void;
}

export function useRetry<T = any>(): UseRetryReturn<T> {
  const [state, setState] = useState<UseRetryState>({
    isRetrying: false,
    attempts: 0,
    lastError: null,
    totalTime: 0,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const reset = useCallback(() => {
    setState({
      isRetrying: false,
      attempts: 0,
      lastError: null,
      totalTime: 0,
    });
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    setState(prev => ({
      ...prev,
      isRetrying: false,
    }));
  }, []);

  const retry = useCallback(async (
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<RetryResult<T>> => {
    // Create abort controller for this retry operation
    abortControllerRef.current = new AbortController();
    
    setState(prev => ({
      ...prev,
      isRetrying: true,
      attempts: 0,
      lastError: null,
      totalTime: 0,
    }));

    const onRetry = (attempt: number, error: any) => {
      setState(prev => ({
        ...prev,
        attempts: attempt,
        lastError: error,
      }));
      
      // Call user-provided onRetry callback if exists
      if (options.onRetry) {
        options.onRetry(attempt, error);
      }
    };

    try {
      const result = await retryUtils.retry(operation, {
        ...options,
        signal: abortControllerRef.current.signal,
        onRetry,
      });

      setState(prev => ({
        ...prev,
        isRetrying: false,
        attempts: result.attempts,
        lastError: result.success ? null : result.error,
        totalTime: result.totalTime,
      }));

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isRetrying: false,
        lastError: error,
      }));
      
      throw error;
    } finally {
      abortControllerRef.current = null;
    }
  }, []);

  const retryFetch = useCallback(async (
    url: string,
    init?: RequestInit,
    options: RetryOptions = {}
  ): Promise<RetryResult<Response>> => {
    // Create abort controller for this retry operation
    abortControllerRef.current = new AbortController();
    
    setState(prev => ({
      ...prev,
      isRetrying: true,
      attempts: 0,
      lastError: null,
      totalTime: 0,
    }));

    const onRetry = (attempt: number, error: any) => {
      setState(prev => ({
        ...prev,
        attempts: attempt,
        lastError: error,
      }));
      
      // Call user-provided onRetry callback if exists
      if (options.onRetry) {
        options.onRetry(attempt, error);
      }
    };

    try {
      const result = await retryUtils.retryFetch(url, init, {
        ...options,
        signal: abortControllerRef.current.signal,
        onRetry,
      });

      setState(prev => ({
        ...prev,
        isRetrying: false,
        attempts: result.attempts,
        lastError: result.success ? null : result.error,
        totalTime: result.totalTime,
      }));

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isRetrying: false,
        lastError: error,
      }));
      
      throw error;
    } finally {
      abortControllerRef.current = null;
    }
  }, []);

  return {
    state,
    retry,
    retryFetch,
    cancel,
    reset,
  };
}

/**
 * Hook for automatic retry with exponential backoff
 */
export function useAutoRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions & { enabled?: boolean } = {}
): {
  data: T | null;
  error: any;
  isLoading: boolean;
  retry: () => void;
} {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const { retry: retryOperation } = useRetry<T>();

  const executeOperation = useCallback(async () => {
    if (!options.enabled && options.enabled !== undefined) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await retryOperation(operation, options);
      
      if (result.success) {
        setData(result.data!);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [operation, options, retryOperation]);

  const manualRetry = useCallback(() => {
    executeOperation();
  }, [executeOperation]);

  // Auto-execute on mount if enabled
  useState(() => {
    if (options.enabled !== false) {
      executeOperation();
    }
  });

  return {
    data,
    error,
    isLoading,
    retry: manualRetry,
  };
}
