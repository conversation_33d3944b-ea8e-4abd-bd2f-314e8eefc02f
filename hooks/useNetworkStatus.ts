/**
 * Hook for monitoring network connectivity status
 */

import { useEffect, useState, useCallback } from 'react';
import { networkUtils, NetworkInfo, NetworkStatus } from '@/utils/networkUtils';

export interface UseNetworkStatusReturn {
  networkInfo: NetworkInfo;
  isOnline: boolean;
  isOffline: boolean;
  isSlow: boolean;
  refreshStatus: () => Promise<void>;
  testEndpoint: (url: string, timeout?: number) => Promise<boolean>;
}

export function useNetworkStatus(): UseNetworkStatusReturn {
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo>({
    status: NetworkStatus.UNKNOWN,
  });

  const refreshStatus = useCallback(async () => {
    try {
      const info = await networkUtils.getNetworkInfo();
      setNetworkInfo(info);
    } catch (error) {
      console.warn('Failed to refresh network status:', error);
    }
  }, []);

  const testEndpoint = useCallback(async (url: string, timeout?: number): Promise<boolean> => {
    return networkUtils.testEndpoint(url, timeout);
  }, []);

  useEffect(() => {
    // Get initial network status
    refreshStatus();

    // Subscribe to network status changes
    const unsubscribe = networkUtils.addListener((info) => {
      setNetworkInfo(info);
    });

    return unsubscribe;
  }, [refreshStatus]);

  return {
    networkInfo,
    isOnline: networkInfo.status === NetworkStatus.ONLINE,
    isOffline: networkInfo.status === NetworkStatus.OFFLINE,
    isSlow: networkInfo.status === NetworkStatus.SLOW,
    refreshStatus,
    testEndpoint,
  };
}
