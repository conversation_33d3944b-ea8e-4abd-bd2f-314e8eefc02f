/**
 * Demo page to showcase different error handling scenarios
 */

import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Stack, router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { ServerErrorPage } from '@/components/error/ServerErrorPage';
import { NetworkErrorPage } from '@/components/error/NetworkErrorPage';
import { NotFoundPage } from '@/components/error/NotFoundPage';

type ErrorType = 'none' | 'server' | 'network' | 'notfound' | 'javascript';

export default function ErrorDemoScreen() {
  const [errorType, setErrorType] = useState<ErrorType>('none');

  const triggerJavaScriptError = () => {
    // This will trigger the ErrorBoundary
    throw new Error('This is a test JavaScript error for demonstration');
  };

  const simulateServerError = async () => {
    // Simulate a server error retry action
    await new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.7) {
          resolve('success');
        } else {
          reject(new Error('Server error simulation'));
        }
      }, 2000);
    });
  };

  const simulateNetworkError = async () => {
    // Simulate a network error retry action
    await new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.5) {
          resolve('success');
        } else {
          reject(new Error('Network error simulation'));
        }
      }, 1500);
    });
  };

  if (errorType === 'server') {
    return (
      <>
        <Stack.Screen options={{ title: 'Server Error Demo' }} />
        <ServerErrorPage
          errorCode="DEMO_500"
          retryAction={simulateServerError}
          customActions={
            <Button
              title="Back to Demo"
              variant="outline"
              onPress={() => setErrorType('none')}
            />
          }
        />
      </>
    );
  }

  if (errorType === 'network') {
    return (
      <>
        <Stack.Screen options={{ title: 'Network Error Demo' }} />
        <NetworkErrorPage
          retryAction={simulateNetworkError}
          customActions={
            <Button
              title="Back to Demo"
              variant="outline"
              onPress={() => setErrorType('none')}
            />
          }
        />
      </>
    );
  }

  if (errorType === 'notfound') {
    return (
      <>
        <Stack.Screen options={{ title: '404 Demo' }} />
        <NotFoundPage
          customActions={
            <Button
              title="Back to Demo"
              variant="outline"
              onPress={() => setErrorType('none')}
            />
          }
        />
      </>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Error Handling Demo' }} />
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <ThemedText type="title" style={styles.title}>
            Error Handling Demo
          </ThemedText>
          
          <ThemedText style={styles.description}>
            This page demonstrates the comprehensive error handling system. 
            Click the buttons below to see different error scenarios:
          </ThemedText>

          <View style={styles.buttonContainer}>
            <Button
              title="404 Not Found Error"
              onPress={() => setErrorType('notfound')}
              style={styles.demoButton}
            />
            
            <Button
              title="500 Server Error"
              onPress={() => setErrorType('server')}
              style={styles.demoButton}
            />
            
            <Button
              title="Network Error"
              onPress={() => setErrorType('network')}
              style={styles.demoButton}
            />
            
            <Button
              title="JavaScript Error (Error Boundary)"
              onPress={triggerJavaScriptError}
              variant="danger"
              style={styles.demoButton}
            />
          </View>

          <View style={styles.infoContainer}>
            <ThemedText type="subtitle" style={styles.infoTitle}>
              Features Demonstrated:
            </ThemedText>
            
            <View style={styles.featureList}>
              <ThemedText style={styles.featureItem}>
                • Automatic retry with exponential backoff
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • Network connectivity detection
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • User-friendly error messages
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • Search functionality on 404 pages
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • Offline feature suggestions
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • Error logging and monitoring
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • React Error Boundary protection
              </ThemedText>
              <ThemedText style={styles.featureItem}>
                • Responsive design with theming
              </ThemedText>
            </View>
          </View>

          <Button
            title="Go Back to Home"
            variant="outline"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </ScrollView>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    gap: 16,
    marginBottom: 32,
  },
  demoButton: {
    width: '100%',
  },
  infoContainer: {
    marginBottom: 32,
  },
  infoTitle: {
    marginBottom: 16,
  },
  featureList: {
    gap: 8,
  },
  featureItem: {
    fontSize: 14,
    lineHeight: 20,
  },
  backButton: {
    width: '100%',
  },
});
