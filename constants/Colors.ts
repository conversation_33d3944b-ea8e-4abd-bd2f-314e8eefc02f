/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    // Error colors
    error: '#DC2626',
    errorBackground: '#FEF2F2',
    warning: '#D97706',
    warningBackground: '#FFFBEB',
    success: '#059669',
    successBackground: '#ECFDF5',
    // Additional UI colors
    border: '#E5E7EB',
    cardBackground: '#F9FAFB',
    muted: '#6B7280',
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    // Error colors
    error: '#EF4444',
    errorBackground: '#1F1415',
    warning: '#F59E0B',
    warningBackground: '#1C1917',
    success: '#10B981',
    successBackground: '#14251B',
    // Additional UI colors
    border: '#374151',
    cardBackground: '#1F2937',
    muted: '#9CA3AF',
  },
};
