/**
 * 500 Internal Server Error page with retry functionality
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useRetry } from '@/hooks/useRetry';
import { useThemeColor } from '@/hooks/useThemeColor';
import { logApiError, logUserAction } from '@/utils/errorLogger';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';

export interface ServerErrorPageProps {
  title?: string;
  message?: string;
  errorCode?: string;
  retryAction?: () => Promise<void>;
  showContactSupport?: boolean;
  customActions?: React.ReactNode;
}

export function ServerErrorPage({
  title = "Server Error",
  message = "We're experiencing some technical difficulties. Our team has been notified and is working to fix the issue.",
  errorCode,
  retryAction,
  showContactSupport = true,
  customActions,
}: ServerErrorPageProps) {
  const [autoRetryCount, setAutoRetryCount] = useState(0);
  const [nextRetryIn, setNextRetryIn] = useState(0);

  const iconColor = useThemeColor({}, 'error');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const errorBackground = useThemeColor({}, 'errorBackground');

  const { state, retry, reset } = useRetry();

  const handleAutoRetry = async () => {
    if (!retryAction) return;

    logUserAction('server_error_auto_retry', { attempt: autoRetryCount + 1 });

    try {
      const result = await retry(retryAction, {
        maxAttempts: 1, // Single attempt for auto-retry
      });

      if (result.success) {
        logUserAction('server_error_auto_retry_success');
        // Success - you might want to navigate away or refresh the page
        router.replace('/');
      } else {
        setAutoRetryCount(prev => prev + 1);
        logApiError('auto_retry', 500, 'Auto retry failed');
      }
    } catch (error) {
      setAutoRetryCount(prev => prev + 1);
      logApiError('auto_retry', 500, error.message);
    }
  };

  // Auto-retry logic
  useEffect(() => {
    if (retryAction && autoRetryCount < 3) {
      const retryDelay = Math.min(5000 * Math.pow(2, autoRetryCount), 30000); // Exponential backoff, max 30s

      const timer = setTimeout(() => {
        handleAutoRetry();
      }, retryDelay);

      // Countdown timer
      setNextRetryIn(retryDelay / 1000);
      const countdownTimer = setInterval(() => {
        setNextRetryIn(prev => {
          if (prev <= 1) {
            clearInterval(countdownTimer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearTimeout(timer);
        clearInterval(countdownTimer);
      };
    }
  }, [autoRetryCount, retryAction, handleAutoRetry]);

  const handleManualRetry = async () => {
    if (!retryAction) {
      // Default retry: reload the page
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      return;
    }

    logUserAction('server_error_manual_retry');
    reset(); // Reset retry state

    try {
      const result = await retry(retryAction, {
        maxAttempts: 3,
        baseDelay: 1000,
      });

      if (result.success) {
        logUserAction('server_error_manual_retry_success');
        router.replace('/');
      }
    } catch (error) {
      logApiError('manual_retry', 500, error.message);
    }
  };

  const handleGoHome = () => {
    logUserAction('server_error_go_home');
    router.replace('/');
  };

  const handleContactSupport = () => {
    logUserAction('server_error_contact_support');
    router.push('/contact');
  };

  const handleReportIssue = () => {
    logUserAction('server_error_report_issue');
    // You could open a bug report form or email client
    const subject = encodeURIComponent(`Server Error Report - ${errorCode || 'Unknown'}`);
    const body = encodeURIComponent(`I encountered a server error while using the app.\n\nError Code: ${errorCode || 'Unknown'}\nTime: ${new Date().toISOString()}\n\nPlease describe what you were doing when this error occurred:`);

    if (typeof window !== 'undefined') {
      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Error Icon */}
        <View style={styles.iconContainer}>
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={80}
            color={iconColor}
          />
        </View>

        {/* Error Message */}
        <View style={styles.messageContainer}>
          <ThemedText type="title" style={styles.title}>
            {title}
          </ThemedText>

          <ThemedText style={styles.message}>
            {message}
          </ThemedText>

          {errorCode && (
            <View style={[styles.errorCodeContainer, { backgroundColor: errorBackground }]}>
              <ThemedText style={styles.errorCodeLabel}>Error Code:</ThemedText>
              <ThemedText style={styles.errorCode}>{errorCode}</ThemedText>
            </View>
          )}
        </View>

        {/* Retry Section */}
        {retryAction && (
          <View style={[styles.retryContainer, { backgroundColor: cardBackground }]}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Automatic Retry
            </ThemedText>

            {state.isRetrying ? (
              <LoadingSpinner
                message={`Retrying... (Attempt ${state.attempts})`}
                size="small"
              />
            ) : autoRetryCount < 3 && nextRetryIn > 0 ? (
              <ThemedText style={styles.retryInfo}>
                Next automatic retry in {Math.ceil(nextRetryIn)} seconds...
              </ThemedText>
            ) : autoRetryCount >= 3 ? (
              <ThemedText style={styles.retryInfo}>
                Automatic retries exhausted. Please try manually.
              </ThemedText>
            ) : null}
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <Button
            title={state.isRetrying ? "Retrying..." : "Try Again"}
            onPress={handleManualRetry}
            loading={state.isRetrying}
            style={styles.actionButton}
          />

          <Button
            title="Go to Home"
            variant="outline"
            onPress={handleGoHome}
            style={styles.actionButton}
          />
        </View>

        {/* Support Options */}
        {showContactSupport && (
          <View style={[styles.supportContainer, { backgroundColor: cardBackground }]}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Need Help?
            </ThemedText>

            <View style={styles.supportActions}>
              <Button
                title="Contact Support"
                variant="secondary"
                onPress={handleContactSupport}
                style={styles.supportButton}
              />

              <Button
                title="Report Issue"
                variant="ghost"
                onPress={handleReportIssue}
                style={styles.supportButton}
              />
            </View>
          </View>
        )}

        {/* Custom Actions */}
        {customActions && (
          <View style={styles.customActionsContainer}>
            {customActions}
          </View>
        )}

        {/* Status Information */}
        <View style={styles.statusContainer}>
          <ThemedText style={styles.statusText}>
            Check our{' '}
            <ThemedText type="link" onPress={() => router.push('/status')}>
              status page
            </ThemedText>
            {' '}for real-time updates on system availability.
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: '100%',
  },
  iconContainer: {
    marginBottom: 24,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 32,
    maxWidth: 400,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  errorCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  errorCodeLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  errorCode: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  retryContainer: {
    width: '100%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: 'center',
  },
  sectionTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  retryInfo: {
    textAlign: 'center',
    fontSize: 14,
  },
  actionsContainer: {
    width: '100%',
    maxWidth: 400,
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
  },
  supportContainer: {
    width: '100%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  supportActions: {
    flexDirection: 'row',
    gap: 12,
  },
  supportButton: {
    flex: 1,
  },
  customActionsContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 24,
  },
  statusContainer: {
    marginTop: 'auto',
    paddingTop: 24,
  },
  statusText: {
    textAlign: 'center',
    fontSize: 14,
  },
});
