/**
 * Search box component for the 404 error page
 */

import React, { useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  type ViewStyle,
} from 'react-native';
import { router } from 'expo-router';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';

export interface SearchBoxProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  style?: ViewStyle;
}

export function SearchBox({
  placeholder = "Search for menu items...",
  onSearch,
  style,
}: SearchBoxProps) {
  const [query, setQuery] = useState('');
  
  const backgroundColor = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');
  const placeholderColor = useThemeColor({}, 'muted');
  const iconColor = useThemeColor({}, 'icon');

  const handleSearch = () => {
    if (query.trim()) {
      if (onSearch) {
        onSearch(query.trim());
      } else {
        // Default behavior: navigate to search results
        router.push(`/search?q=${encodeURIComponent(query.trim())}`);
      }
    }
  };

  const handleSubmit = () => {
    handleSearch();
  };

  return (
    <View style={[styles.container, style]}>
      <View style={[
        styles.inputContainer,
        {
          backgroundColor,
          borderColor,
        }
      ]}>
        <IconSymbol
          name="magnifyingglass"
          size={20}
          color={iconColor}
          style={styles.searchIcon}
        />
        <TextInput
          style={[
            styles.input,
            { color: textColor }
          ]}
          value={query}
          onChangeText={setQuery}
          placeholder={placeholder}
          placeholderTextColor={placeholderColor}
          onSubmitEditing={handleSubmit}
          returnKeyType="search"
          autoCapitalize="none"
          autoCorrect={false}
        />
        {query.length > 0 && (
          <Button
            title="Search"
            size="small"
            onPress={handleSearch}
            style={styles.searchButton}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: 400,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    minHeight: 24,
  },
  searchButton: {
    marginLeft: 8,
  },
});
