/**
 * React Error Boundary component for catching and handling JavaScript errors
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { errorLogger, ErrorSeverity } from '@/utils/errorLogger';
import React, { Component, ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: any, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    this.setState({
      error,
      errorInfo,
    });

    // Log the error
    errorLogger.logError({
      severity: ErrorSeverity.CRITICAL,
      message: `React Error Boundary: ${error.message}`,
      stack: error.stack,
      additionalData: {
        type: 'react_error_boundary',
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      },
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(
          this.state.error!,
          this.state.errorInfo,
          this.handleRetry
        );
      }

      // Default error UI
      return (
        <ThemedView style={styles.container}>
          <View style={styles.content}>
            <ThemedText type="title" style={styles.title}>
              Oops! Something went wrong
            </ThemedText>

            <ThemedText style={styles.message}>
              We&apos;re sorry, but something unexpected happened. The error has been logged and we&apos;ll look into it.
            </ThemedText>

            {__DEV__ && this.state.error && (
              <View style={styles.errorDetails}>
                <ThemedText type="subtitle" style={styles.errorTitle}>
                  Error Details (Development Only):
                </ThemedText>
                <ThemedText style={styles.errorText}>
                  {this.state.error.message}
                </ThemedText>
                {this.state.error.stack && (
                  <ThemedText style={styles.stackTrace}>
                    {this.state.error.stack}
                  </ThemedText>
                )}
              </View>
            )}

            <View style={styles.actions}>
              <Button
                title="Try Again"
                onPress={this.handleRetry}
                style={styles.retryButton}
              />

              <Button
                title="Go to Home"
                variant="outline"
                onPress={() => {
                  // Navigate to home - you might need to adjust this based on your navigation setup
                  if (typeof window !== 'undefined') {
                    window.location.href = '/';
                  }
                }}
                style={styles.homeButton}
              />
            </View>
          </View>
        </ThemedView>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    maxWidth: 400,
    width: '100%',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  errorDetails: {
    width: '100%',
    marginBottom: 24,
    padding: 16,
    backgroundColor: 'rgba(220, 38, 38, 0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(220, 38, 38, 0.2)',
  },
  errorTitle: {
    marginBottom: 8,
    color: '#DC2626',
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    marginBottom: 8,
  },
  stackTrace: {
    fontSize: 12,
    color: '#DC2626',
    fontFamily: 'monospace',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  retryButton: {
    flex: 1,
  },
  homeButton: {
    flex: 1,
  },
});

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}
