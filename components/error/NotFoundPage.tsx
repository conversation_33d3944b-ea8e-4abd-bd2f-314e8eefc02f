/**
 * Enhanced 404 Not Found page with search functionality and navigation options
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { logUserAction } from '@/utils/errorLogger';
import { Link, router } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SearchBox } from './SearchBox';

export interface NotFoundPageProps {
  title?: string;
  message?: string;
  showSearch?: boolean;
  showNavigation?: boolean;
  customActions?: React.ReactNode;
}

export function NotFoundPage({
  title = "Page Not Found",
  message = "Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.",
  showSearch = true,
  showNavigation = true,
  customActions,
}: NotFoundPageProps) {
  const iconColor = useThemeColor({}, 'muted');
  const cardBackground = useThemeColor({}, 'cardBackground');

  const handleSearch = (query: string) => {
    logUserAction('404_search', { query });

    // Navigate to search results
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  const handleGoHome = () => {
    logUserAction('404_go_home');
    router.replace('/');
  };

  const handleGoBack = () => {
    logUserAction('404_go_back');
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/');
    }
  };

  const popularPages = [
    { title: 'Menu', href: '/menu', icon: 'list.bullet' },
    { title: 'Favorites', href: '/favorites', icon: 'heart' },
    { title: 'Orders', href: '/orders', icon: 'bag' },
    { title: 'Profile', href: '/profile', icon: 'person' },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Error Icon */}
        <View style={styles.iconContainer}>
          <IconSymbol
            name="exclamationmark.triangle"
            size={80}
            color={iconColor}
          />
        </View>

        {/* Error Message */}
        <View style={styles.messageContainer}>
          <ThemedText type="title" style={styles.title}>
            {title}
          </ThemedText>

          <ThemedText style={styles.message}>
            {message}
          </ThemedText>
        </View>

        {/* Search Box */}
        {showSearch && (
          <View style={styles.searchContainer}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Search for what you need
            </ThemedText>
            <SearchBox
              placeholder="Search menu items, restaurants..."
              onSearch={handleSearch}
            />
          </View>
        )}

        {/* Navigation Options */}
        {showNavigation && (
          <View style={styles.navigationContainer}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Quick Navigation
            </ThemedText>

            <View style={styles.quickActions}>
              <Button
                title="Go Home"
                onPress={handleGoHome}
                style={styles.actionButton}
              />

              <Button
                title="Go Back"
                variant="outline"
                onPress={handleGoBack}
                style={styles.actionButton}
              />
            </View>

            {/* Popular Pages */}
            <View style={[styles.popularPages, { backgroundColor: cardBackground }]}>
              <ThemedText type="defaultSemiBold" style={styles.popularTitle}>
                Popular Pages
              </ThemedText>

              <View style={styles.pageGrid}>
                {popularPages.map((page, index) => (
                  <Link
                    key={index}
                    href={page.href}
                    style={styles.pageLink}
                    onPress={() => logUserAction('404_popular_page_click', { page: page.title })}
                  >
                    <View style={styles.pageLinkContent}>
                      <IconSymbol
                        name={page.icon as any}
                        size={24}
                        color={iconColor}
                      />
                      <ThemedText style={styles.pageLinkText}>
                        {page.title}
                      </ThemedText>
                    </View>
                  </Link>
                ))}
              </View>
            </View>
          </View>
        )}

        {/* Custom Actions */}
        {customActions && (
          <View style={styles.customActionsContainer}>
            {customActions}
          </View>
        )}

        {/* Help Text */}
        <View style={styles.helpContainer}>
          <ThemedText style={styles.helpText}>
            Still can&apos;t find what you&apos;re looking for?{' '}
            <Link href="/contact" style={styles.helpLink}>
              <ThemedText type="link">Contact Support</ThemedText>
            </Link>
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: '100%',
  },
  iconContainer: {
    marginBottom: 24,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 32,
    maxWidth: 400,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
  },
  searchContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 32,
  },
  sectionTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  navigationContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 32,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
  },
  popularPages: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  popularTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  pageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  pageLink: {
    flex: 1,
    minWidth: '45%',
  },
  pageLinkContent: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  pageLinkText: {
    marginTop: 8,
    textAlign: 'center',
    fontSize: 14,
  },
  customActionsContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 24,
  },
  helpContainer: {
    marginTop: 'auto',
    paddingTop: 24,
  },
  helpText: {
    textAlign: 'center',
    fontSize: 14,
  },
  helpLink: {
    textDecorationLine: 'underline',
  },
});
