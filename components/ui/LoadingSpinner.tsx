/**
 * Loading spinner component with theming support
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import {
    ActivityIndicator,
    StyleSheet,
    View,
    type ViewStyle,
} from 'react-native';

export interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
  style?: ViewStyle;
}

export function LoadingSpinner({
  size = 'large',
  color,
  message,
  overlay = false,
  style,
}: LoadingSpinnerProps) {
  const defaultColor = useThemeColor({}, 'tint');
  const spinnerColor = color || defaultColor;

  const content = (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={spinnerColor} />
      {message && (
        <ThemedText style={styles.message} type="default">
          {message}
        </ThemedText>
      )}
    </View>
  );

  if (overlay) {
    return (
      <ThemedView style={styles.overlay}>
        {content}
      </ThemedView>
    );
  }

  return content;
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  message: {
    marginTop: 12,
    textAlign: 'center',
  },
});
