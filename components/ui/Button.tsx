/**
 * Reusable Button component with theming support
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  type TouchableOpacityProps,
  type TextStyle,
  type ViewStyle,
} from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
  lightColor?: string;
  darkColor?: string;
}

export function Button({
  title,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  style,
  textStyle,
  lightColor,
  darkColor,
  onPress,
  ...rest
}: ButtonProps) {
  const backgroundColor = useThemeColor(
    { light: lightColor, dark: darkColor },
    getBackgroundColorKey(variant)
  );
  
  const textColor = useThemeColor(
    {},
    getTextColorKey(variant)
  );

  const borderColor = useThemeColor(
    {},
    variant === 'outline' ? 'border' : undefined
  );

  const isDisabled = disabled || loading;

  const handlePress = (event: any) => {
    if (!isDisabled && onPress) {
      onPress(event);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        styles[size],
        {
          backgroundColor: variant === 'ghost' ? 'transparent' : backgroundColor,
          borderColor: variant === 'outline' ? borderColor : 'transparent',
          borderWidth: variant === 'outline' ? 1 : 0,
          opacity: isDisabled ? 0.6 : 1,
        },
        style,
      ]}
      onPress={handlePress}
      disabled={isDisabled}
      activeOpacity={0.7}
      {...rest}
    >
      {leftIcon && !loading && leftIcon}
      
      {loading ? (
        <ActivityIndicator
          size="small"
          color={textColor}
          style={styles.loader}
        />
      ) : (
        <Text
          style={[
            styles.text,
            styles[`${size}Text`],
            { color: textColor },
            textStyle,
          ]}
        >
          {title}
        </Text>
      )}
      
      {rightIcon && !loading && rightIcon}
    </TouchableOpacity>
  );
}

function getBackgroundColorKey(variant: ButtonProps['variant']): string {
  switch (variant) {
    case 'primary':
      return 'tint';
    case 'secondary':
      return 'cardBackground';
    case 'outline':
    case 'ghost':
      return 'background';
    case 'danger':
      return 'error';
    default:
      return 'tint';
  }
}

function getTextColorKey(variant: ButtonProps['variant']): string {
  switch (variant) {
    case 'primary':
      return 'background'; // White text on primary background
    case 'secondary':
    case 'outline':
    case 'ghost':
      return 'text';
    case 'danger':
      return 'background'; // White text on danger background
    default:
      return 'background';
  }
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  small: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  medium: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  large: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 10,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  smallText: {
    fontSize: 14,
    lineHeight: 20,
  },
  mediumText: {
    fontSize: 16,
    lineHeight: 24,
  },
  largeText: {
    fontSize: 18,
    lineHeight: 28,
  },
  loader: {
    marginHorizontal: 4,
  },
});
