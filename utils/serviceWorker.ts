/**
 * Service Worker registration and management utilities
 */

import { Platform } from 'react-native';
import { logError, logUserAction } from './errorLogger';

export interface ServiceWorkerConfig {
  swUrl?: string;
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  onError?: (error: Error) => void;
}

class ServiceWorkerManager {
  private isRegistered = false;
  private registration: ServiceWorkerRegistration | null = null;

  /**
   * Register the service worker (web only)
   */
  async register(config: ServiceWorkerConfig = {}): Promise<void> {
    // Only register on web platform
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return;
    }

    // Check if service workers are supported
    if (!('serviceWorker' in navigator)) {
      console.warn('Service workers are not supported in this browser');
      return;
    }

    const swUrl = config.swUrl || '/sw.js';

    try {
      const registration = await navigator.serviceWorker.register(swUrl);
      this.registration = registration;
      this.isRegistered = true;

      logUserAction('service_worker_registered');

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const installingWorker = registration.installing;
        
        if (installingWorker) {
          installingWorker.addEventListener('statechange', () => {
            if (installingWorker.state === 'installed') {
              if (navigator.serviceWorker.controller) {
                // New content is available
                logUserAction('service_worker_update_available');
                if (config.onUpdate) {
                  config.onUpdate(registration);
                }
              } else {
                // Content is cached for the first time
                logUserAction('service_worker_content_cached');
                if (config.onSuccess) {
                  config.onSuccess(registration);
                }
              }
            }
          });
        }
      });

      // Listen for controlling service worker changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        logUserAction('service_worker_controller_changed');
        // Reload the page to get the new content
        window.location.reload();
      });

      console.log('Service Worker registered successfully');
      
    } catch (error) {
      this.isRegistered = false;
      logError({
        severity: 'medium' as any,
        message: `Service Worker registration failed: ${error.message}`,
        additionalData: { type: 'service_worker_error' },
      });
      
      if (config.onError) {
        config.onError(error as Error);
      }
    }
  }

  /**
   * Unregister the service worker
   */
  async unregister(): Promise<void> {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return;
    }

    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.unregister();
        this.isRegistered = false;
        this.registration = null;
        logUserAction('service_worker_unregistered');
      } catch (error) {
        logError({
          severity: 'medium' as any,
          message: `Service Worker unregistration failed: ${error.message}`,
          additionalData: { type: 'service_worker_error' },
        });
      }
    }
  }

  /**
   * Update the service worker
   */
  async update(): Promise<void> {
    if (!this.registration) {
      return;
    }

    try {
      await this.registration.update();
      logUserAction('service_worker_update_requested');
    } catch (error) {
      logError({
        severity: 'medium' as any,
        message: `Service Worker update failed: ${error.message}`,
        additionalData: { type: 'service_worker_error' },
      });
    }
  }

  /**
   * Check if service worker is registered
   */
  isServiceWorkerRegistered(): boolean {
    return this.isRegistered;
  }

  /**
   * Get the current registration
   */
  getRegistration(): ServiceWorkerRegistration | null {
    return this.registration;
  }

  /**
   * Send a message to the service worker
   */
  async sendMessage(message: any): Promise<void> {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return;
    }

    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage(message);
    }
  }

  /**
   * Listen for messages from the service worker
   */
  onMessage(callback: (event: MessageEvent) => void): () => void {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {};
    }

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', callback);
      
      return () => {
        navigator.serviceWorker.removeEventListener('message', callback);
      };
    }

    return () => {};
  }

  /**
   * Check if the app is running in standalone mode (PWA)
   */
  isStandalone(): boolean {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return false;
    }

    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true
    );
  }

  /**
   * Prompt user to install the PWA
   */
  async promptInstall(): Promise<boolean> {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return false;
    }

    // Check if the app is already installed
    if (this.isStandalone()) {
      return false;
    }

    // Check if the install prompt is available
    const deferredPrompt = (window as any).deferredPrompt;
    
    if (deferredPrompt) {
      try {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        logUserAction('pwa_install_prompt', { outcome });
        
        // Clear the deferred prompt
        (window as any).deferredPrompt = null;
        
        return outcome === 'accepted';
      } catch (error) {
        logError({
          severity: 'low' as any,
          message: `PWA install prompt failed: ${error.message}`,
          additionalData: { type: 'pwa_install_error' },
        });
        return false;
      }
    }

    return false;
  }

  /**
   * Setup PWA install prompt handling
   */
  setupInstallPrompt(): () => void {
    if (Platform.OS !== 'web' || typeof window === 'undefined') {
      return () => {};
    }

    const handleBeforeInstallPrompt = (event: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      event.preventDefault();
      // Stash the event so it can be triggered later
      (window as any).deferredPrompt = event;
      logUserAction('pwa_install_prompt_available');
    };

    const handleAppInstalled = () => {
      logUserAction('pwa_installed');
      // Clear the deferred prompt
      (window as any).deferredPrompt = null;
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }
}

// Export singleton instance
export const serviceWorkerManager = new ServiceWorkerManager();

// Export convenience functions
export const registerServiceWorker = serviceWorkerManager.register.bind(serviceWorkerManager);
export const unregisterServiceWorker = serviceWorkerManager.unregister.bind(serviceWorkerManager);
export const updateServiceWorker = serviceWorkerManager.update.bind(serviceWorkerManager);
export const isServiceWorkerRegistered = serviceWorkerManager.isServiceWorkerRegistered.bind(serviceWorkerManager);
export const promptPWAInstall = serviceWorkerManager.promptInstall.bind(serviceWorkerManager);
