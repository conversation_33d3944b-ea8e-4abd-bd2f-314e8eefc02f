/**
 * Retry utilities with exponential backoff and jitter
 * Provides robust retry mechanisms for network requests and other operations
 */

import { logNetworkError } from './errorLogger';

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  jitter?: boolean;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
  signal?: AbortSignal;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

export class RetryError extends Error {
  public attempts: number;
  public lastError: any;

  constructor(message: string, attempts: number, lastError: any) {
    super(message);
    this.name = 'RetryError';
    this.attempts = attempts;
    this.lastError = lastError;
  }
}

class RetryUtils {
  private defaultOptions: Required<Omit<RetryOptions, 'retryCondition' | 'onRetry' | 'signal'>> = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    jitter: true,
  };

  /**
   * Retry an async operation with exponential backoff
   */
  async retry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        // Check if operation was aborted
        if (options.signal?.aborted) {
          throw new Error('Operation aborted');
        }

        const data = await operation();
        
        return {
          success: true,
          data,
          attempts: attempt,
          totalTime: Date.now() - startTime,
        };
      } catch (error) {
        lastError = error;

        // Check if we should retry this error
        if (options.retryCondition && !options.retryCondition(error)) {
          break;
        }

        // Don't retry if this is the last attempt
        if (attempt === config.maxAttempts) {
          break;
        }

        // Call retry callback if provided
        if (options.onRetry) {
          try {
            options.onRetry(attempt, error);
          } catch (callbackError) {
            console.warn('Retry callback error:', callbackError);
          }
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt, config);
        
        // Log retry attempt
        logNetworkError(`Retry attempt ${attempt} failed, retrying in ${delay}ms`, {
          attempt,
          error: error.message,
          delay,
        });

        // Wait before next attempt
        await this.sleep(delay);
      }
    }

    return {
      success: false,
      error: new RetryError(
        `Operation failed after ${config.maxAttempts} attempts`,
        config.maxAttempts,
        lastError
      ),
      attempts: config.maxAttempts,
      totalTime: Date.now() - startTime,
    };
  }

  /**
   * Retry a fetch request with specific network error handling
   */
  async retryFetch(
    url: string,
    init?: RequestInit,
    options: RetryOptions = {}
  ): Promise<RetryResult<Response>> {
    const retryCondition = (error: any) => {
      // Retry on network errors, timeouts, and 5xx status codes
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return true; // Network error
      }
      if (error.name === 'AbortError') {
        return false; // Don't retry aborted requests
      }
      if (error.status >= 500 && error.status < 600) {
        return true; // Server errors
      }
      if (error.status === 429) {
        return true; // Rate limited
      }
      return false;
    };

    return this.retry(async () => {
      const response = await fetch(url, init);
      
      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        (error as any).status = response.status;
        (error as any).response = response;
        throw error;
      }
      
      return response;
    }, {
      retryCondition,
      ...options,
    });
  }

  /**
   * Create a retry wrapper for any async function
   */
  withRetry<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    options: RetryOptions = {}
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const result = await this.retry(() => fn(...args), options);
      
      if (result.success) {
        return result.data!;
      } else {
        throw result.error;
      }
    };
  }

  /**
   * Calculate delay with exponential backoff and optional jitter
   */
  private calculateDelay(attempt: number, config: Required<Omit<RetryOptions, 'retryCondition' | 'onRetry' | 'signal'>>): number {
    // Exponential backoff: baseDelay * (backoffFactor ^ (attempt - 1))
    let delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
    
    // Apply maximum delay limit
    delay = Math.min(delay, config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (config.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.floor(delay);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create an AbortController that times out after specified milliseconds
   */
  createTimeoutController(timeoutMs: number): AbortController {
    const controller = new AbortController();
    
    setTimeout(() => {
      controller.abort();
    }, timeoutMs);
    
    return controller;
  }

  /**
   * Retry with timeout
   */
  async retryWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    retryOptions: RetryOptions = {}
  ): Promise<RetryResult<T>> {
    const controller = this.createTimeoutController(timeoutMs);
    
    return this.retry(operation, {
      ...retryOptions,
      signal: controller.signal,
    });
  }

  /**
   * Batch retry multiple operations
   */
  async retryBatch<T>(
    operations: (() => Promise<T>)[],
    options: RetryOptions = {}
  ): Promise<RetryResult<T>[]> {
    const promises = operations.map(operation => this.retry(operation, options));
    return Promise.all(promises);
  }

  /**
   * Retry with circuit breaker pattern
   */
  createCircuitBreaker<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    options: {
      failureThreshold?: number;
      resetTimeout?: number;
      retryOptions?: RetryOptions;
    } = {}
  ): (...args: T) => Promise<R> {
    const config = {
      failureThreshold: 5,
      resetTimeout: 60000,
      ...options,
    };

    let failureCount = 0;
    let lastFailureTime = 0;
    let state: 'closed' | 'open' | 'half-open' = 'closed';

    return async (...args: T): Promise<R> => {
      const now = Date.now();

      // Check if circuit should be reset
      if (state === 'open' && now - lastFailureTime > config.resetTimeout) {
        state = 'half-open';
        failureCount = 0;
      }

      // Reject immediately if circuit is open
      if (state === 'open') {
        throw new Error('Circuit breaker is open');
      }

      try {
        const result = await this.retry(() => fn(...args), config.retryOptions);
        
        if (result.success) {
          // Reset on success
          failureCount = 0;
          state = 'closed';
          return result.data!;
        } else {
          throw result.error;
        }
      } catch (error) {
        failureCount++;
        lastFailureTime = now;

        if (failureCount >= config.failureThreshold) {
          state = 'open';
        }

        throw error;
      }
    };
  }
}

// Export singleton instance
export const retryUtils = new RetryUtils();

// Export convenience functions
export const retry = retryUtils.retry.bind(retryUtils);
export const retryFetch = retryUtils.retryFetch.bind(retryUtils);
export const withRetry = retryUtils.withRetry.bind(retryUtils);
export const createTimeoutController = retryUtils.createTimeoutController.bind(retryUtils);
