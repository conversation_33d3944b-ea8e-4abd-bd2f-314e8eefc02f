/**
 * Error logging and monitoring utilities for the food app
 * Provides comprehensive error tracking with different severity levels
 */

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ErrorLogEntry {
  id: string;
  timestamp: Date;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  userAgent?: string;
  url?: string;
  userId?: string;
  sessionId?: string;
  additionalData?: Record<string, any>;
}

class ErrorLogger {
  private logs: ErrorLogEntry[] = [];
  private maxLogs = 100;
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.logError({
          severity: ErrorSeverity.HIGH,
          message: `Unhandled Promise Rejection: ${event.reason}`,
          stack: event.reason?.stack,
          additionalData: { type: 'unhandledrejection' },
        });
      });

      // Handle global JavaScript errors
      window.addEventListener('error', (event) => {
        this.logError({
          severity: ErrorSeverity.HIGH,
          message: event.message,
          stack: event.error?.stack,
          url: event.filename,
          additionalData: {
            type: 'javascript_error',
            lineno: event.lineno,
            colno: event.colno,
          },
        });
      });
    }
  }

  logError({
    severity,
    message,
    stack,
    url,
    userId,
    additionalData,
  }: Omit<ErrorLogEntry, 'id' | 'timestamp' | 'userAgent' | 'sessionId'>): void {
    const errorEntry: ErrorLogEntry = {
      id: this.generateErrorId(),
      timestamp: new Date(),
      severity,
      message,
      stack,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: url || (typeof window !== 'undefined' ? window.location.href : undefined),
      userId,
      sessionId: this.sessionId,
      additionalData,
    };

    this.logs.push(errorEntry);
    this.trimLogs();
    this.reportError(errorEntry);

    // Console logging for development
    if (__DEV__) {
      console.group(`🚨 Error [${severity.toUpperCase()}]`);
      console.error(message);
      if (stack) console.error(stack);
      if (additionalData) console.table(additionalData);
      console.groupEnd();
    }
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private trimLogs(): void {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private async reportError(errorEntry: ErrorLogEntry): Promise<void> {
    // In a real app, you would send this to your error reporting service
    // Examples: Sentry, Bugsnag, LogRocket, etc.
    
    try {
      // Example API call (replace with your actual error reporting endpoint)
      if (!__DEV__ && typeof fetch !== 'undefined') {
        await fetch('/api/errors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(errorEntry),
        });
      }
    } catch (reportingError) {
      // Silently fail if error reporting fails
      console.warn('Failed to report error:', reportingError);
    }
  }

  getLogs(): ErrorLogEntry[] {
    return [...this.logs];
  }

  getLogsBySeverity(severity: ErrorSeverity): ErrorLogEntry[] {
    return this.logs.filter(log => log.severity === severity);
  }

  clearLogs(): void {
    this.logs = [];
  }

  // Convenience methods for different error types
  logNetworkError(message: string, additionalData?: Record<string, any>): void {
    this.logError({
      severity: ErrorSeverity.MEDIUM,
      message: `Network Error: ${message}`,
      additionalData: { type: 'network_error', ...additionalData },
    });
  }

  logApiError(endpoint: string, status: number, message: string): void {
    this.logError({
      severity: ErrorSeverity.MEDIUM,
      message: `API Error: ${message}`,
      additionalData: {
        type: 'api_error',
        endpoint,
        status,
      },
    });
  }

  logUserAction(action: string, additionalData?: Record<string, any>): void {
    this.logError({
      severity: ErrorSeverity.LOW,
      message: `User Action: ${action}`,
      additionalData: { type: 'user_action', ...additionalData },
    });
  }

  logPerformanceIssue(metric: string, value: number, threshold: number): void {
    this.logError({
      severity: ErrorSeverity.MEDIUM,
      message: `Performance Issue: ${metric} (${value}ms) exceeded threshold (${threshold}ms)`,
      additionalData: {
        type: 'performance_issue',
        metric,
        value,
        threshold,
      },
    });
  }
}

// Export singleton instance
export const errorLogger = new ErrorLogger();

// Export convenience functions
export const logError = errorLogger.logError.bind(errorLogger);
export const logNetworkError = errorLogger.logNetworkError.bind(errorLogger);
export const logApiError = errorLogger.logApiError.bind(errorLogger);
export const logUserAction = errorLogger.logUserAction.bind(errorLogger);
export const logPerformanceIssue = errorLogger.logPerformanceIssue.bind(errorLogger);
