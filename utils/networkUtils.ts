/**
 * Network utilities for detecting connectivity and handling network-related operations
 */

import { Platform } from 'react-native';
import { logNetworkError } from './errorLogger';

export enum NetworkStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  SLOW = 'slow',
  UNKNOWN = 'unknown',
}

export interface NetworkInfo {
  status: NetworkStatus;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

class NetworkUtils {
  private listeners: Set<(info: NetworkInfo) => void> = new Set();
  private currentStatus: NetworkStatus = NetworkStatus.UNKNOWN;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeNetworkMonitoring();
  }

  private initializeNetworkMonitoring(): void {
    if (Platform.OS === 'web') {
      this.setupWebNetworkMonitoring();
    } else {
      this.setupNativeNetworkMonitoring();
    }
  }

  private setupWebNetworkMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Listen to online/offline events
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // Check initial status
    this.updateNetworkStatus();

    // Set up periodic connectivity checks
    this.startPeriodicChecks();
  }

  private setupNativeNetworkMonitoring(): void {
    // For React Native, you would typically use @react-native-community/netinfo
    // This is a placeholder implementation
    this.updateNetworkStatus();
    this.startPeriodicChecks();
  }

  private handleOnline = (): void => {
    this.currentStatus = NetworkStatus.ONLINE;
    this.notifyListeners();
  };

  private handleOffline = (): void => {
    this.currentStatus = NetworkStatus.OFFLINE;
    this.notifyListeners();
  };

  private startPeriodicChecks(): void {
    // Check network status every 30 seconds
    this.checkInterval = setInterval(() => {
      this.updateNetworkStatus();
    }, 30000);
  }

  private async updateNetworkStatus(): Promise<void> {
    try {
      const networkInfo = await this.getNetworkInfo();
      const previousStatus = this.currentStatus;
      this.currentStatus = networkInfo.status;

      if (previousStatus !== this.currentStatus) {
        this.notifyListeners();
      }
    } catch (error) {
      logNetworkError('Failed to update network status', { error: error.message });
    }
  }

  private notifyListeners(): void {
    const networkInfo = this.getCurrentNetworkInfo();
    this.listeners.forEach(listener => {
      try {
        listener(networkInfo);
      } catch (error) {
        console.warn('Network listener error:', error);
      }
    });
  }

  async getNetworkInfo(): Promise<NetworkInfo> {
    if (Platform.OS === 'web' && typeof navigator !== 'undefined') {
      return this.getWebNetworkInfo();
    } else {
      return this.getNativeNetworkInfo();
    }
  }

  private async getWebNetworkInfo(): Promise<NetworkInfo> {
    const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
    
    if (!isOnline) {
      return { status: NetworkStatus.OFFLINE };
    }

    // Check connection quality using Network Information API
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      const effectiveType = connection.effectiveType;
      const downlink = connection.downlink;
      const rtt = connection.rtt;
      const saveData = connection.saveData;

      let status = NetworkStatus.ONLINE;
      if (effectiveType === 'slow-2g' || effectiveType === '2g' || rtt > 1000) {
        status = NetworkStatus.SLOW;
      }

      return {
        status,
        effectiveType,
        downlink,
        rtt,
        saveData,
      };
    }

    // Fallback: Test actual connectivity
    try {
      const response = await fetch('/ping', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000),
      });
      
      return {
        status: response.ok ? NetworkStatus.ONLINE : NetworkStatus.SLOW,
      };
    } catch {
      return { status: NetworkStatus.OFFLINE };
    }
  }

  private async getNativeNetworkInfo(): Promise<NetworkInfo> {
    // For React Native, you would use @react-native-community/netinfo
    // This is a placeholder implementation
    try {
      // Simulate network check
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      
      return {
        status: response.ok ? NetworkStatus.ONLINE : NetworkStatus.SLOW,
      };
    } catch {
      return { status: NetworkStatus.OFFLINE };
    }
  }

  getCurrentNetworkInfo(): NetworkInfo {
    return {
      status: this.currentStatus,
    };
  }

  isOnline(): boolean {
    return this.currentStatus === NetworkStatus.ONLINE;
  }

  isOffline(): boolean {
    return this.currentStatus === NetworkStatus.OFFLINE;
  }

  isSlow(): boolean {
    return this.currentStatus === NetworkStatus.SLOW;
  }

  addListener(listener: (info: NetworkInfo) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  removeAllListeners(): void {
    this.listeners.clear();
  }

  destroy(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
    }

    this.removeAllListeners();
  }

  // Utility method to test if a specific endpoint is reachable
  async testEndpoint(url: string, timeout: number = 5000): Promise<boolean> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(timeout),
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  // Get network speed estimate (web only)
  async estimateNetworkSpeed(): Promise<number | null> {
    if (Platform.OS !== 'web' || typeof navigator === 'undefined') {
      return null;
    }

    const connection = (navigator as any).connection;
    if (connection && connection.downlink) {
      return connection.downlink; // Mbps
    }

    // Fallback: measure download speed
    try {
      const startTime = performance.now();
      await fetch('/test-image.jpg', { cache: 'no-cache' });
      const endTime = performance.now();
      
      // Rough estimate based on a small test file
      const duration = (endTime - startTime) / 1000; // seconds
      const fileSize = 0.1; // MB (estimated)
      return fileSize / duration; // Mbps
    } catch {
      return null;
    }
  }
}

// Export singleton instance
export const networkUtils = new NetworkUtils();

// Export convenience functions
export const getNetworkInfo = networkUtils.getNetworkInfo.bind(networkUtils);
export const isOnline = networkUtils.isOnline.bind(networkUtils);
export const isOffline = networkUtils.isOffline.bind(networkUtils);
export const testEndpoint = networkUtils.testEndpoint.bind(networkUtils);
