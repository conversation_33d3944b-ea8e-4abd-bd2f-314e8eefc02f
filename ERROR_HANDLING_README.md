# Comprehensive Error Handling System

This document describes the comprehensive error handling system implemented for the food app. The system provides robust error handling with user-friendly interfaces, automatic retry mechanisms, and offline support.

## Features

### 🚨 Error Pages
- **404 Not Found Page**: Enhanced with search functionality and navigation options
- **500 Server Error Page**: Automatic retry with exponential backoff
- **Network Error Page**: Connectivity detection and offline features

### 🔄 Retry Mechanisms
- Exponential backoff with jitter
- Automatic and manual retry options
- Circuit breaker pattern support
- Network-aware retry strategies

### 📱 Offline Support
- Service worker for caching
- Offline feature suggestions
- Network connectivity detection
- Progressive Web App (PWA) capabilities

### 📊 Error Monitoring
- Comprehensive error logging
- Different severity levels
- Session tracking
- Performance monitoring

## Components

### Error Pages

#### NotFoundPage
```tsx
import { NotFoundPage } from '@/components/error/NotFoundPage';

<NotFoundPage
  title="Custom 404 Title"
  message="Custom message"
  showSearch={true}
  showNavigation={true}
  customActions={<CustomButton />}
/>
```

#### ServerErrorPage
```tsx
import { ServerErrorPage } from '@/components/error/ServerErrorPage';

<ServerErrorPage
  title="Server Error"
  errorCode="500"
  retryAction={async () => {
    // Your retry logic here
    await retryApiCall();
  }}
  showContactSupport={true}
/>
```

#### NetworkErrorPage
```tsx
import { NetworkErrorPage } from '@/components/error/NetworkErrorPage';

<NetworkErrorPage
  retryAction={async () => {
    // Your retry logic here
    await retryNetworkCall();
  }}
  showOfflineFeatures={true}
/>
```

### Error Boundary

Wrap your components with ErrorBoundary to catch JavaScript errors:

```tsx
import { ErrorBoundary } from '@/components/error/ErrorBoundary';

<ErrorBoundary
  fallback={(error, errorInfo, retry) => (
    <CustomErrorUI error={error} onRetry={retry} />
  )}
  onError={(error, errorInfo) => {
    // Custom error handling
    console.error('Error caught by boundary:', error);
  }}
>
  <YourComponent />
</ErrorBoundary>
```

### UI Components

#### Button
```tsx
import { Button } from '@/components/ui/Button';

<Button
  title="Retry"
  variant="primary"
  size="medium"
  loading={isLoading}
  onPress={handleRetry}
  leftIcon={<Icon name="refresh" />}
/>
```

#### LoadingSpinner
```tsx
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

<LoadingSpinner
  size="large"
  message="Retrying..."
  overlay={true}
/>
```

## Hooks

### useNetworkStatus
Monitor network connectivity:

```tsx
import { useNetworkStatus } from '@/hooks/useNetworkStatus';

function MyComponent() {
  const { isOnline, isOffline, isSlow, networkInfo, refreshStatus } = useNetworkStatus();
  
  if (isOffline) {
    return <NetworkErrorPage />;
  }
  
  return <YourContent />;
}
```

### useRetry
Handle retry operations with state management:

```tsx
import { useRetry } from '@/hooks/useRetry';

function MyComponent() {
  const { state, retry, cancel, reset } = useRetry();
  
  const handleRetry = async () => {
    const result = await retry(
      () => fetchData(),
      {
        maxAttempts: 3,
        baseDelay: 1000,
        backoffFactor: 2,
      }
    );
    
    if (result.success) {
      console.log('Data:', result.data);
    } else {
      console.error('Failed after retries:', result.error);
    }
  };
  
  return (
    <div>
      <Button 
        title="Fetch Data" 
        onPress={handleRetry}
        loading={state.isRetrying}
      />
      {state.isRetrying && (
        <p>Attempt {state.attempts} of 3...</p>
      )}
    </div>
  );
}
```

## Utilities

### Error Logger
```tsx
import { logError, logNetworkError, logApiError } from '@/utils/errorLogger';

// Log general errors
logError({
  severity: 'high',
  message: 'Something went wrong',
  additionalData: { userId: '123' }
});

// Log network errors
logNetworkError('Connection timeout', { endpoint: '/api/data' });

// Log API errors
logApiError('/api/users', 500, 'Internal server error');
```

### Retry Utils
```tsx
import { retry, retryFetch, withRetry } from '@/utils/retryUtils';

// Retry any async operation
const result = await retry(
  () => fetchUserData(),
  {
    maxAttempts: 3,
    baseDelay: 1000,
    retryCondition: (error) => error.status >= 500
  }
);

// Retry fetch requests
const response = await retryFetch('/api/data', {
  method: 'POST',
  body: JSON.stringify(data)
});

// Create a retry wrapper
const retryableFetch = withRetry(fetch, { maxAttempts: 3 });
```

### Network Utils
```tsx
import { getNetworkInfo, isOnline, testEndpoint } from '@/utils/networkUtils';

// Check network status
const networkInfo = await getNetworkInfo();
console.log('Network status:', networkInfo.status);

// Test specific endpoint
const isReachable = await testEndpoint('https://api.example.com/health');
```

## Service Worker

The service worker provides offline functionality:

### Registration
```tsx
import { registerServiceWorker } from '@/utils/serviceWorker';

registerServiceWorker({
  onSuccess: () => console.log('SW registered'),
  onUpdate: () => console.log('SW update available'),
  onError: (error) => console.error('SW error:', error)
});
```

### PWA Installation
```tsx
import { promptPWAInstall, serviceWorkerManager } from '@/utils/serviceWorker';

// Setup install prompt
const cleanup = serviceWorkerManager.setupInstallPrompt();

// Prompt user to install
const installed = await promptPWAInstall();
```

## Configuration

### Colors
Error-related colors are defined in `constants/Colors.ts`:

```tsx
export const Colors = {
  light: {
    error: '#DC2626',
    errorBackground: '#FEF2F2',
    warning: '#D97706',
    warningBackground: '#FFFBEB',
    success: '#059669',
    successBackground: '#ECFDF5',
  },
  // ... dark theme colors
};
```

### Routing
Error pages are automatically handled by the routing system:

- `app/+not-found.tsx` - 404 errors
- Custom error pages can be created for specific routes

## Best Practices

1. **Always wrap components in ErrorBoundary** for JavaScript error protection
2. **Use retry mechanisms** for network requests and API calls
3. **Provide offline alternatives** when possible
4. **Log errors appropriately** with proper severity levels
5. **Test error scenarios** during development
6. **Monitor error rates** in production
7. **Provide clear user feedback** during retry attempts
8. **Cache critical content** for offline use

## Demo

Visit `/error-demo` in the app to see all error handling features in action.

## Dependencies

The error handling system uses the following key dependencies:
- React Native / Expo
- Expo Router for navigation
- React hooks for state management
- Service Workers for offline support (web only)

## Browser Support

- **Modern browsers**: Full support including service workers
- **Older browsers**: Graceful degradation without service worker features
- **Mobile**: Full support on iOS and Android through React Native
